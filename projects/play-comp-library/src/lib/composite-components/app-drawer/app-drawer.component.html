<ava-drawer
  [isOpen]="isOpen"
  [position]="position"
  [size]="size"
  [showCloseButton]="showCloseButton"
  [closeOnOverlayClick]="closeOnOverlayClick"
  [closeOnEscape]="closeOnEscape"
  [showHeader]="false"
  [showFooter]="false"
  customClass="app-drawer"
  (opened)="onDrawerOpened()"
  (closed)="onDrawerClosed()">

  <!-- Custom Header with Close Button -->
  <div class="app-drawer__header">
    <div class="app-drawer__header-content">
      <h1 class="app-drawer__title">{{ data.title }}</h1>
      <p class="app-drawer__subtitle">{{ data.subtitle }}</p>
    </div>
    <div class="app-drawer__close-section" *ngIf="showCloseButton">
      <ava-button
        iconName="X"
        iconPosition="only"
        variant="secondary"
        size="small"
        [pill]="true"
        (userClick)="close()"
        aria-label="Close drawer">
      </ava-button>
    </div>
  </div>

  <!-- Add to List Button -->
  <div class="app-drawer__add-to-list">
    <ava-button
      [label]="data.addToListText || 'Add to my list'"
      iconName="plus"
      iconPosition="left"
      variant="secondary"
      size="medium"
      [outlined]="true"
      [pill]="true"
      (userClick)="onAddToListClick()">
    </ava-button>
  </div>

  <!-- Tags Section -->
  <div class="app-drawer__tags">
    <ava-tag-chip
      *ngFor="let tag of data.tags; let i = index"
      [label]="tag"
      [variant]="getTagVariant(i)"
      size="small"
      shape="pill">
    </ava-tag-chip>
  </div>

  <!-- Stats Grid -->
  <div class="app-drawer__stats-grid">
    <!-- Category -->
    <div class="app-drawer__stat-item">
      <div class="app-drawer__stat-header">
        <ava-icon iconName="chevron-left" [iconSize]="16" iconColor="#9ca3af"></ava-icon>
        <ava-icon iconName="chevron-right" [iconSize]="16" iconColor="#9ca3af"></ava-icon>
      </div>
      <div class="app-drawer__stat-label">{{ data.category }}</div>
      <div class="app-drawer__stat-value">{{ data.type }}</div>
    </div>

    <!-- Developed By -->
    <div class="app-drawer__stat-item">
      <div class="app-drawer__stat-header">
        <ava-icon iconName="user" [iconSize]="16" iconColor="#9ca3af"></ava-icon>
      </div>
      <div class="app-drawer__stat-label">{{ data.developedBy }}</div>
      <div class="app-drawer__stat-value">{{ data.name }}</div>
    </div>

    <!-- Relevancy -->
    <div class="app-drawer__stat-item">
      <div class="app-drawer__stat-header">
        <ava-icon iconName="target" [iconSize]="16" iconColor="#9ca3af"></ava-icon>
      </div>
      <div class="app-drawer__stat-label">Relevancy</div>
      <div class="app-drawer__stat-value">{{ data.relevancy }}</div>
    </div>

    <!-- Rating -->
    <div class="app-drawer__stat-item">
      <div class="app-drawer__stat-header">
        <ava-icon iconName="star" [iconSize]="16" iconColor="#9ca3af"></ava-icon>
      </div>
      <div class="app-drawer__stat-label">Rating</div>
      <div class="app-drawer__stat-value">
        <ava-rating
          [rating]="data.rating"
          [maxRating]="data.maxRating"
          size="small"
          [showText]="true"
          [readonly]="true">
        </ava-rating>
      </div>
      <div class="app-drawer__stat-subvalue">{{ data.outOf }}</div>
    </div>
  </div>

  <!-- Description Section -->
  <div class="app-drawer__description-section">
    <h3 class="app-drawer__section-title">What it's for</h3>
    <div class="app-drawer__description">
      <p>{{ data.description }}</p>
    </div>
  </div>

  <!-- Action Button -->
  <div class="app-drawer__action-section">
    <ava-button
      [label]="data.actionButtonText"
      [variant]="data.actionButtonVariant || 'primary'"
      size="large"
      width="100%"
      [pill]="true"
      (userClick)="onActionButtonClick()">
    </ava-button>
  </div>

</ava-drawer>
