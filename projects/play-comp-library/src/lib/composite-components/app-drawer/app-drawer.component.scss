/* ===================================================================
   APP DRAWER COMPONENT STYLES

   Styles for the app drawer showcase component that matches the
   reference design with proper spacing, typography, and layout
   =================================================================== */

.app-drawer {
  /* Custom drawer styling */
  .ava-drawer__body {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0;
  }
}

/* ===================================================================
   HEADER SECTION
   =================================================================== */
.app-drawer__header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #f3f4f6;
  background: #ffffff;
}

.app-drawer__header-content {
  flex: 1;
  min-width: 0;
}

.app-drawer__title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.25;
  color: #111827;
}

.app-drawer__subtitle {
  margin: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
  color: #6b7280;
}

.app-drawer__close-section {
  margin-left: 16px;
  flex-shrink: 0;
}

/* ===================================================================
   ADD TO LIST SECTION
   =================================================================== */
.app-drawer__add-to-list {
  padding: 16px 24px;
  border-bottom: 1px solid #f3f4f6;
}

/* ===================================================================
   TAGS SECTION
   =================================================================== */
.app-drawer__tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 16px 24px;
  border-bottom: 1px solid #f3f4f6;
}

/* ===================================================================
   STATS GRID SECTION
   =================================================================== */
.app-drawer__stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
  border-bottom: 1px solid #f3f4f6;
}

.app-drawer__stat-item {
  padding: 20px 24px;
  border-right: 1px solid #f3f4f6;
  border-bottom: 1px solid #f3f4f6;

  &:nth-child(even) {
    border-right: none;
  }

  &:nth-last-child(-n+2) {
    border-bottom: none;
  }
}

.app-drawer__stat-header {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
  height: 16px;
}

.app-drawer__stat-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.app-drawer__stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  line-height: 1.25;
}

.app-drawer__stat-subvalue {
  font-size: 12px;
  font-weight: 400;
  color: #6b7280;
  margin-top: 2px;
}

/* ===================================================================
   DESCRIPTION SECTION
   =================================================================== */
.app-drawer__description-section {
  padding: 24px;
  background: #fef7f7;
  flex: 1;
}

.app-drawer__section-title {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.app-drawer__description {
  font-size: 14px;
  line-height: 1.6;
  color: #374151;

  p {
    margin: 0;
  }
}

/* ===================================================================
   ACTION SECTION
   =================================================================== */
.app-drawer__action-section {
  padding: 24px;
  background: #ffffff;
  border-top: 1px solid #f3f4f6;
  margin-top: auto;
}

/* ===================================================================
   RESPONSIVE DESIGN
   =================================================================== */
@media (max-width: 768px) {
  .app-drawer__header {
    padding: 16px;
  }

  .app-drawer__add-to-list {
    padding: 12px 16px;
  }

  .app-drawer__tags {
    padding: 12px 16px;
  }

  .app-drawer__stat-item {
    padding: 16px;
  }

  .app-drawer__description-section {
    padding: 16px;
  }

  .app-drawer__action-section {
    padding: 16px;
  }

  .app-drawer__title {
    font-size: 20px;
  }

  .app-drawer__stats-grid {
    grid-template-columns: 1fr;
  }

  .app-drawer__stat-item {
    border-right: none;
    border-bottom: 1px solid #f3f4f6;

    &:last-child {
      border-bottom: none;
    }
  }
}

/* ===================================================================
   DARK THEME SUPPORT
   =================================================================== */
@media (prefers-color-scheme: dark) {
  .app-drawer__header {
    background: #1f2937;
    border-bottom-color: #374151;
  }

  .app-drawer__title {
    color: #f9fafb;
  }

  .app-drawer__subtitle {
    color: #d1d5db;
  }

  .app-drawer__add-to-list {
    border-bottom-color: #374151;
  }

  .app-drawer__tags {
    border-bottom-color: #374151;
  }

  .app-drawer__tag {
    background: #374151;
    color: #d1d5db;

    &:first-child {
      background: #1e3a8a;
      color: #93c5fd;
    }
  }

  .app-drawer__stats-grid {
    border-bottom-color: #374151;
  }

  .app-drawer__stat-item {
    border-right-color: #374151;
    border-bottom-color: #374151;
  }

  .app-drawer__stat-label {
    color: #9ca3af;
  }

  .app-drawer__stat-value {
    color: #f9fafb;
  }

  .app-drawer__stat-subvalue {
    color: #9ca3af;
  }

  .app-drawer__description-section {
    background: #1f2937;
  }

  .app-drawer__section-title {
    color: #f9fafb;
  }

  .app-drawer__description {
    color: #d1d5db;
  }

  .app-drawer__action-section {
    background: #1f2937;
    border-top-color: #374151;
  }
}
