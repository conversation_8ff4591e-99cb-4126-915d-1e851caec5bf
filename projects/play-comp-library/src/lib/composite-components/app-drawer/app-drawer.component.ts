import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  ChangeDetectionStrategy,
  ViewEncapsulation
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { DrawerComponent } from '../../components/drawer/drawer.component';
import { ButtonComponent } from '../../components/button/button.component';
import { IconComponent } from '../../components/icon/icon.component';
import { RatingComponent } from '../../components/rating/rating.component';
import { TagChipComponent } from '../../components/tag-chip/tag-chip.component';

export interface AppDrawerData {
  title: string;
  subtitle: string;
  description: string;
  tags: string[];
  rating: number;
  maxRating: number;
  ratingText: string;
  category: string;
  developedBy: string;
  relevancy: string;
  type: string;
  name: string;
  score: string;
  outOf: string;
  actionButtonText: string;
  actionButtonVariant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  addToListText?: string;
}

@Component({
  selector: 'ava-app-drawer',
  standalone: true,
  imports: [CommonModule, DrawerComponent, ButtonComponent, IconComponent, RatingComponent, TagChipComponent],
  templateUrl: './app-drawer.component.html',
  styleUrls: ['./app-drawer.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppDrawerComponent implements OnInit {

  // Drawer configuration
  @Input() isOpen: boolean = false;
  @Input() position: 'left' | 'right' | 'top' | 'bottom' = 'right';
  @Input() size: 'small' | 'medium' | 'large' | 'extra-large' | 'full' = 'large';
  @Input() showCloseButton: boolean = true;
  @Input() closeOnOverlayClick: boolean = true;
  @Input() closeOnEscape: boolean = true;

  // Content data
  @Input() data: AppDrawerData = {
    title: 'Create Angular Component',
    subtitle: 'Effortlessly convert Ruby code to Spring Boot with optimised migration',
    description: 'A agent that converts Ruby code to Spring Boot can be highly beneficial for organisation\'s migrating from Ruby on Rails to Java Spring Boot. However, the effectiveness depends on several factors, including the complexity of the application, language differences, and the capabilities of the conversion agent.',
    tags: ['Development', 'Front End', 'Framework', '98% Accuracy', '#4 in agents'],
    rating: 4.5,
    maxRating: 5,
    ratingText: '4.5 ★',
    category: 'Category',
    developedBy: 'Developed by',
    relevancy: '9.5/10',
    type: 'Type',
    name: 'Name',
    score: 'Score',
    outOf: 'Out of 5',
    actionButtonText: 'Go to Playground',
    actionButtonVariant: 'primary',
    addToListText: 'Add to my list'
  };

  // Events
  @Output() opened = new EventEmitter<void>();
  @Output() closed = new EventEmitter<void>();
  @Output() actionButtonClick = new EventEmitter<void>();
  @Output() addToListClick = new EventEmitter<void>();

  ngOnInit(): void {
    // Component initialization
  }

  /**
   * Handle drawer opened event
   */
  onDrawerOpened(): void {
    this.opened.emit();
  }

  /**
   * Handle drawer closed event
   */
  onDrawerClosed(): void {
    this.closed.emit();
  }

  /**
   * Handle action button click
   */
  onActionButtonClick(): void {
    this.actionButtonClick.emit();
  }

  /**
   * Handle add to list button click
   */
  onAddToListClick(): void {
    this.addToListClick.emit();
  }

  /**
   * Open the drawer
   */
  open(): void {
    this.isOpen = true;
  }

  /**
   * Close the drawer
   */
  close(): void {
    this.isOpen = false;
  }

  /**
   * Toggle the drawer
   */
  toggle(): void {
    this.isOpen = !this.isOpen;
  }

  /**
   * Generate star rating array for display
   */
  getStarRating(): { filled: boolean }[] {
    const stars = [];
    for (let i = 1; i <= this.data.maxRating; i++) {
      stars.push({ filled: i <= Math.floor(this.data.rating) });
    }
    return stars;
  }

  /**
   * Check if rating has half star
   */
  hasHalfStar(): boolean {
    return this.data.rating % 1 !== 0;
  }

  /**
   * Get the position of half star
   */
  getHalfStarPosition(): number {
    return Math.floor(this.data.rating);
  }
}
