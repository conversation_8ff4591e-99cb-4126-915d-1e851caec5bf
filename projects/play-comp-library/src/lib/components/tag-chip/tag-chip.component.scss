/* ===================================================================
   AVA TAG CHIP COMPONENT STYLES
   
   Flexible tag/chip component with variants, sizes, and interactions
   =================================================================== */

.ava-tag-chip {
  display: inline-flex;
  align-items: center;
  gap: var(--tag-chip-gap, 6px);
  padding: var(--tag-chip-padding, 6px 12px);
  font-family: var(--tag-chip-font-family, inherit);
  font-size: var(--tag-chip-font-size, 12px);
  font-weight: var(--tag-chip-font-weight, 500);
  line-height: 1.2;
  border: var(--tag-chip-border, 1px solid transparent);
  border-radius: var(--tag-chip-border-radius, 6px);
  background: var(--tag-chip-background, #f3f4f6);
  color: var(--tag-chip-color, #374151);
  transition: all 0.15s ease;
  white-space: nowrap;
  user-select: none;
  box-sizing: border-box;

  /* ===================================================================
     SIZE VARIANTS
     =================================================================== */
  &--small {
    --tag-chip-padding: 4px 8px;
    --tag-chip-font-size: 11px;
    --tag-chip-gap: 4px;
    --tag-chip-border-radius: 4px;
  }

  &--medium {
    --tag-chip-padding: 6px 12px;
    --tag-chip-font-size: 12px;
    --tag-chip-gap: 6px;
    --tag-chip-border-radius: 6px;
  }

  &--large {
    --tag-chip-padding: 8px 16px;
    --tag-chip-font-size: 14px;
    --tag-chip-gap: 8px;
    --tag-chip-border-radius: 8px;
  }

  /* ===================================================================
     SHAPE VARIANTS
     =================================================================== */
  &--pill {
    --tag-chip-border-radius: 999px;
  }

  &--square {
    --tag-chip-border-radius: 0;
  }

  /* ===================================================================
     COLOR VARIANTS
     =================================================================== */
  &--primary {
    --tag-chip-background: #dbeafe;
    --tag-chip-color: #1e40af;
    --tag-chip-border: 1px solid #93c5fd;
  }

  &--secondary {
    --tag-chip-background: #f1f5f9;
    --tag-chip-color: #475569;
    --tag-chip-border: 1px solid #cbd5e1;
  }

  &--success {
    --tag-chip-background: #d1fae5;
    --tag-chip-color: #065f46;
    --tag-chip-border: 1px solid #86efac;
  }

  &--warning {
    --tag-chip-background: #fef3c7;
    --tag-chip-color: #92400e;
    --tag-chip-border: 1px solid #fcd34d;
  }

  &--danger {
    --tag-chip-background: #fee2e2;
    --tag-chip-color: #991b1b;
    --tag-chip-border: 1px solid #fca5a5;
  }

  &--info {
    --tag-chip-background: #e0e7ff;
    --tag-chip-color: #3730a3;
    --tag-chip-border: 1px solid #a5b4fc;
  }

  /* ===================================================================
     INTERACTIVE STATES
     =================================================================== */
  &--clickable {
    cursor: pointer;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    &:focus {
      outline: 2px solid var(--color-brand-primary, #3b82f6);
      outline-offset: 2px;
    }
  }

  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  &--removable {
    padding-right: var(--tag-chip-removable-padding, 4px);
  }
}

/* ===================================================================
   LABEL STYLES
   =================================================================== */
.ava-tag-chip__label {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ===================================================================
   ICON STYLES
   =================================================================== */
.ava-tag-chip__icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  &--left {
    margin-left: -2px;
  }

  &--right {
    margin-right: -2px;
  }
}

/* ===================================================================
   REMOVE BUTTON STYLES
   =================================================================== */
.ava-tag-chip__remove {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  margin: -2px -4px -2px 2px;
  border: none;
  border-radius: 50%;
  background: transparent;
  color: inherit;
  cursor: pointer;
  transition: all 0.15s ease;
  flex-shrink: 0;

  &:hover {
    background: rgba(0, 0, 0, 0.1);
  }

  &:active {
    background: rgba(0, 0, 0, 0.2);
  }

  &:focus {
    outline: 2px solid var(--color-brand-primary, #3b82f6);
    outline-offset: 1px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.ava-tag-chip__remove-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ===================================================================
   ACCESSIBILITY
   =================================================================== */
@media (prefers-reduced-motion: reduce) {
  .ava-tag-chip {
    transition: none;
  }

  .ava-tag-chip__remove {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .ava-tag-chip {
    border: 2px solid;
  }

  .ava-tag-chip:focus {
    outline: 3px solid;
  }
}

/* ===================================================================
   DARK THEME SUPPORT
   =================================================================== */
@media (prefers-color-scheme: dark) {
  .ava-tag-chip {
    --tag-chip-background: #374151;
    --tag-chip-color: #d1d5db;
    --tag-chip-border: 1px solid #4b5563;

    &--primary {
      --tag-chip-background: #1e3a8a;
      --tag-chip-color: #93c5fd;
      --tag-chip-border: 1px solid #3b82f6;
    }

    &--secondary {
      --tag-chip-background: #475569;
      --tag-chip-color: #e2e8f0;
      --tag-chip-border: 1px solid #64748b;
    }

    &--success {
      --tag-chip-background: #065f46;
      --tag-chip-color: #86efac;
      --tag-chip-border: 1px solid #10b981;
    }

    &--warning {
      --tag-chip-background: #92400e;
      --tag-chip-color: #fcd34d;
      --tag-chip-border: 1px solid #f59e0b;
    }

    &--danger {
      --tag-chip-background: #991b1b;
      --tag-chip-color: #fca5a5;
      --tag-chip-border: 1px solid #ef4444;
    }

    &--info {
      --tag-chip-background: #3730a3;
      --tag-chip-color: #a5b4fc;
      --tag-chip-border: 1px solid #6366f1;
    }
  }
}
