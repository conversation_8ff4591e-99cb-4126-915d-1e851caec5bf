<div 
  [class]="getTagClasses()"
  (click)="onChipClick($event)"
  (keydown)="onKeyDown($event)"
  [attr.tabindex]="(clickable || removable) && !disabled ? 0 : -1"
  [attr.role]="clickable ? 'button' : 'text'"
  [attr.aria-disabled]="disabled"
  [attr.aria-label]="label + (removable ? ', removable' : '')">

  <!-- Left Icon -->
  <ava-icon
    *ngIf="icon && iconPosition === 'left'"
    [iconName]="icon"
    [iconSize]="getIconSize()"
    class="ava-tag-chip__icon ava-tag-chip__icon--left">
  </ava-icon>

  <!-- Label -->
  <span class="ava-tag-chip__label">{{ label }}</span>

  <!-- Right Icon -->
  <ava-icon
    *ngIf="icon && iconPosition === 'right'"
    [iconName]="icon"
    [iconSize]="getIconSize()"
    class="ava-tag-chip__icon ava-tag-chip__icon--right">
  </ava-icon>

  <!-- Remove Button -->
  <button
    *ngIf="removable"
    type="button"
    class="ava-tag-chip__remove"
    (click)="onRemoveClick($event)"
    [disabled]="disabled"
    [attr.aria-label]="'Remove ' + label">
    <ava-icon
      [iconName]="removeIcon"
      [iconSize]="getIconSize()"
      class="ava-tag-chip__remove-icon">
    </ava-icon>
  </button>

</div>
