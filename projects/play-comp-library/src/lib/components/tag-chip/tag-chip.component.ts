import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
  ViewEncapsulation
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

export type TagChipSize = 'small' | 'medium' | 'large';
export type TagChipVariant = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';
export type TagChipShape = 'rounded' | 'pill' | 'square';

@Component({
  selector: 'ava-tag-chip',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './tag-chip.component.html',
  styleUrls: ['./tag-chip.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TagChipComponent {
  
  @Input() label: string = '';
  @Input() size: TagChipSize = 'medium';
  @Input() variant: TagChipVariant = 'default';
  @Input() shape: TagChipShape = 'rounded';
  @Input() removable: boolean = false;
  @Input() clickable: boolean = false;
  @Input() disabled: boolean = false;
  @Input() icon: string = '';
  @Input() iconPosition: 'left' | 'right' = 'left';
  @Input() removeIcon: string = 'X';
  @Input() customClass: string = '';

  @Output() click = new EventEmitter<Event>();
  @Output() remove = new EventEmitter<Event>();

  /**
   * Get the CSS classes for the tag chip
   */
  getTagClasses(): string {
    const classes = [
      'ava-tag-chip',
      `ava-tag-chip--${this.size}`,
      `ava-tag-chip--${this.variant}`,
      `ava-tag-chip--${this.shape}`
    ];

    if (this.clickable && !this.disabled) {
      classes.push('ava-tag-chip--clickable');
    }

    if (this.disabled) {
      classes.push('ava-tag-chip--disabled');
    }

    if (this.removable) {
      classes.push('ava-tag-chip--removable');
    }

    if (this.customClass) {
      classes.push(this.customClass);
    }

    return classes.join(' ');
  }

  /**
   * Get the icon size based on chip size
   */
  getIconSize(): number {
    switch (this.size) {
      case 'small':
        return 12;
      case 'large':
        return 18;
      case 'medium':
      default:
        return 14;
    }
  }

  /**
   * Handle chip click
   */
  onChipClick(event: Event): void {
    if (!this.disabled && this.clickable) {
      this.click.emit(event);
    }
  }

  /**
   * Handle remove button click
   */
  onRemoveClick(event: Event): void {
    if (!this.disabled) {
      event.stopPropagation();
      this.remove.emit(event);
    }
  }

  /**
   * Handle keyboard events
   */
  onKeyDown(event: KeyboardEvent): void {
    if (this.disabled) return;

    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (this.clickable) {
        this.click.emit(event);
      }
    }

    if (event.key === 'Delete' || event.key === 'Backspace') {
      if (this.removable) {
        event.preventDefault();
        this.remove.emit(event);
      }
    }
  }
}
