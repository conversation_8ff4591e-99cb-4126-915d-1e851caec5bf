/* ===================================================================
   AVA RATING COMPONENT STYLES
   
   Flexible rating component with stars, text, and interactive features
   =================================================================== */

.ava-rating {
  display: inline-flex;
  align-items: center;
  gap: var(--rating-gap, 8px);
  font-family: var(--rating-font-family, inherit);

  /* ===================================================================
     SIZE VARIANTS
     =================================================================== */
  &--small {
    --rating-gap: 6px;
    --rating-star-gap: 2px;
    --rating-text-size: 12px;
  }

  &--medium {
    --rating-gap: 8px;
    --rating-star-gap: 4px;
    --rating-text-size: 14px;
  }

  &--large {
    --rating-gap: 10px;
    --rating-star-gap: 6px;
    --rating-text-size: 16px;
  }

  /* ===================================================================
     VARIANT STYLES
     =================================================================== */
  &--compact {
    --rating-gap: 4px;
    --rating-star-gap: 2px;
  }

  &--detailed {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  /* ===================================================================
     READONLY STATE
     =================================================================== */
  &--readonly {
    .ava-rating__star {
      cursor: default;
    }
  }
}

/* ===================================================================
   STARS CONTAINER
   =================================================================== */
.ava-rating__stars {
  display: flex;
  align-items: center;
  gap: var(--rating-star-gap, 4px);
}

.ava-rating__star {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.15s ease;

  &--clickable {
    cursor: pointer;

    &:hover {
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }

    &:focus {
      outline: 2px solid var(--color-brand-primary, #3b82f6);
      outline-offset: 2px;
      border-radius: 2px;
    }
  }
}

.ava-rating__icon {
  transition: color 0.15s ease;
}

/* ===================================================================
   TEXT SECTION
   =================================================================== */
.ava-rating__text {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: var(--rating-text-size, 14px);
  line-height: 1.4;
}

.ava-rating__value {
  font-weight: 600;
  color: var(--rating-value-color, #111827);
}

.ava-rating__separator {
  color: var(--rating-separator-color, #9ca3af);
  font-weight: 400;
}

.ava-rating__count {
  color: var(--rating-count-color, #6b7280);
  font-weight: 400;
}

/* ===================================================================
   INTERACTIVE STATES
   =================================================================== */
.ava-rating:not(.ava-rating--readonly) {
  .ava-rating__star {
    &:hover ~ .ava-rating__star .ava-rating__icon {
      color: var(--rating-empty-color, #e5e7eb) !important;
    }

    &:hover .ava-rating__icon {
      color: var(--rating-hover-color, #fbbf24) !important;
    }
  }
}

/* ===================================================================
   ACCESSIBILITY
   =================================================================== */
@media (prefers-reduced-motion: reduce) {
  .ava-rating__star {
    transition: none;
  }

  .ava-rating__icon {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .ava-rating__star:focus {
    outline: 3px solid;
  }
}

/* ===================================================================
   DARK THEME SUPPORT
   =================================================================== */
@media (prefers-color-scheme: dark) {
  .ava-rating {
    --rating-value-color: #f9fafb;
    --rating-separator-color: #6b7280;
    --rating-count-color: #9ca3af;
    --rating-empty-color: #374151;
  }
}

/* ===================================================================
   CUSTOM PROPERTIES FALLBACKS
   =================================================================== */
.ava-rating {
  --rating-gap: 8px;
  --rating-star-gap: 4px;
  --rating-text-size: 14px;
  --rating-value-color: #111827;
  --rating-separator-color: #9ca3af;
  --rating-count-color: #6b7280;
  --rating-empty-color: #e5e7eb;
  --rating-filled-color: #fbbf24;
  --rating-hover-color: #f59e0b;
}
