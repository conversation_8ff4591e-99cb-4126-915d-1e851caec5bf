<div [class]="getRatingClasses()">

  <!-- Stars Container -->
  <div class="ava-rating__stars">
    <div
      *ngFor="let star of getStars(); let i = index"
      class="ava-rating__star"
      [class.ava-rating__star--clickable]="!readonly"
      (click)="onStarClick(star.index)"
      (mouseenter)="onStarHover(star.index)"
      [attr.aria-label]="'Rate ' + star.index + ' star' + (star.index === 1 ? '' : 's')"
      [attr.tabindex]="readonly ? -1 : 0">

      <ava-icon
        [iconName]="getIconName(star.type)"
        [iconColor]="getIconColor(star.type)"
        [iconSize]="getIconSize()"
        class="ava-rating__icon">
      </ava-icon>
    </div>
  </div>

  <!-- Rating Text -->
  <div *ngIf="showText || showCount" class="ava-rating__text">
    <span *ngIf="showText" class="ava-rating__value">
      {{ getRatingText() }}
    </span>
    <span *ngIf="showText && showCount" class="ava-rating__separator">
      ·
    </span>
    <span *ngIf="showCount" class="ava-rating__count">
      {{ getCountText() }}
    </span>
  </div>

</div>
